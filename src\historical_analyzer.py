"""
历史分析模块
负责跨年度历史数据分析和趋势分析
"""

import pandas as pd
import numpy as np
from typing import Dict, List, Union, Tuple, Optional
from datetime import datetime, timedelta
import logging
import sys
import os
from pathlib import Path

# 添加项目根目录到路径
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))
import config

class HistoricalAnalyzer:
    """历史分析器类"""
    
    def __init__(self, data: pd.DataFrame):
        """
        初始化历史分析器
        
        Args:
            data: 多级索引的DataFrame，索引为(date, sector_code)
        """
        self.data = data
        self.logger = self._setup_logger()
        
        # 获取可用日期
        self.available_dates = sorted(data.index.get_level_values('date').unique())
        self.date_range = (self.available_dates[0], self.available_dates[-1])
        
        # 按年份组织数据
        self.yearly_data = self._organize_data_by_year()
        
        self.logger.info(f"历史分析器初始化完成")
        self.logger.info(f"数据范围: {self.date_range[0]} 到 {self.date_range[1]}")
        self.logger.info(f"涵盖年份: {sorted(self.yearly_data.keys())}")
    
    def _setup_logger(self) -> logging.Logger:
        """设置日志记录器"""
        logger = logging.getLogger(__name__)
        if not logger.handlers:
            handler = logging.StreamHandler()
            formatter = logging.Formatter(
                '%(asctime)s - %(name)s - %(levelname)s - %(message)s'
            )
            handler.setFormatter(formatter)
            logger.addHandler(handler)
            logger.setLevel(logging.INFO)
        return logger
    
    def _organize_data_by_year(self) -> Dict[int, pd.DataFrame]:
        """按年份组织数据"""
        yearly_data = {}
        
        for date in self.available_dates:
            year = pd.to_datetime(date).year
            if year not in yearly_data:
                yearly_data[year] = []
            yearly_data[year].append(date)
        
        # 为每年创建数据子集
        for year in yearly_data:
            year_dates = yearly_data[year]
            yearly_data[year] = self.data.loc[
                self.data.index.get_level_values('date').isin(year_dates)
            ]
        
        return yearly_data
    
    def calculate_historical_windows(self, target_date: Union[str, pd.Timestamp],
                                   window_days_list: List[int]) -> Dict:
        """
        计算历史同期时间窗口分析

        Args:
            target_date: 目标日期
            window_days_list: 时间窗口天数列表

        Returns:
            包含历史分析结果的字典
        """
        if isinstance(target_date, str):
            target_date = pd.to_datetime(target_date)

        self.logger.info(f"开始历史同期分析，目标日期: {target_date.strftime('%Y-%m-%d')}")

        # 获取目标日期的月日
        target_month = target_date.month
        target_day = target_date.day

        # 查找历年同期日期
        historical_dates = self._find_historical_dates(target_month, target_day)

        results = {
            'target_date': target_date,
            'target_month_day': f"{target_month:02d}-{target_day:02d}",
            'historical_dates': historical_dates,
            'window_analysis': {},
            'yearly_comparison': {},
            'trend_analysis': {},
            'multi_year_rankings': {}  # 新增：多年份排行榜数据
        }

        # 对每个时间窗口进行历史分析
        for window_days in window_days_list:
            self.logger.info(f"分析 {window_days} 日时间窗口的历史表现")

            window_results = self._analyze_window_across_years(
                historical_dates, window_days
            )

            results['window_analysis'][window_days] = window_results

            # 生成多年份排行榜数据
            results['multi_year_rankings'][window_days] = self._generate_multi_year_rankings(
                window_results
            )

        # 生成年度对比分析
        results['yearly_comparison'] = self._generate_yearly_comparison(
            results['window_analysis']
        )

        # 生成趋势分析
        results['trend_analysis'] = self._generate_trend_analysis(
            results['window_analysis']
        )

        self.logger.info(f"历史分析完成，涵盖 {len(historical_dates)} 个年份")
        return results
    
    def _find_historical_dates(self, target_month: int, target_day: int) -> Dict[int, pd.Timestamp]:
        """查找历年同期日期"""
        historical_dates = {}
        
        for year in sorted(self.yearly_data.keys()):
            year_dates = [pd.to_datetime(d) for d in self.yearly_data[year].index.get_level_values('date').unique()]
            
            # 寻找最接近目标月日的日期
            target_date_this_year = pd.Timestamp(year=year, month=target_month, day=target_day)
            
            # 找到最接近的实际交易日
            closest_date = None
            min_diff = float('inf')
            
            for date in year_dates:
                diff = abs((date - target_date_this_year).days)
                if diff < min_diff and diff <= 5:  # 允许5天的偏差
                    min_diff = diff
                    closest_date = date
            
            if closest_date:
                historical_dates[year] = closest_date
        
        return historical_dates
    
    def _analyze_window_across_years(self, historical_dates: Dict[int, pd.Timestamp], 
                                   window_days: int) -> Dict:
        """分析时间窗口在各年的表现"""
        yearly_performance = {}
        
        for year, end_date in historical_dates.items():
            try:
                # 获取该年的时间窗口数据
                performance = self._calculate_single_window_performance(
                    end_date, window_days, year
                )
                
                if not performance.empty:
                    yearly_performance[year] = {
                        'end_date': end_date,
                        'performance': performance,
                        'stats': self._calculate_performance_stats(performance)
                    }
                    
            except Exception as e:
                self.logger.warning(f"计算 {year} 年时间窗口失败: {str(e)}")
                continue
        
        return yearly_performance
    
    def _calculate_single_window_performance(self, end_date: pd.Timestamp, 
                                           window_days: int, year: int) -> pd.DataFrame:
        """计算单个时间窗口的表现"""
        # 获取该年的数据
        year_data = self.yearly_data[year]
        year_dates = sorted(year_data.index.get_level_values('date').unique())
        
        # 找到结束日期在该年数据中的位置
        end_date_str = end_date.strftime('%Y-%m-%d')
        if end_date_str not in year_dates:
            # 找到最接近的日期
            end_date_ts = pd.to_datetime(end_date_str)
            valid_dates = [d for d in year_dates if pd.to_datetime(d) <= end_date_ts]
            if not valid_dates:
                return pd.DataFrame()
            end_date_str = max(valid_dates)
        
        # 获取时间窗口内的交易日
        end_idx = year_dates.index(end_date_str)
        start_idx = max(0, end_idx - window_days + 1)
        window_dates = year_dates[start_idx:end_idx + 1]
        
        # 筛选时间窗口内的数据
        window_data = year_data.loc[
            year_data.index.get_level_values('date').isin(window_dates)
        ]
        
        if window_data.empty:
            return pd.DataFrame()
        
        # 计算每个板块的表现
        performance_results = []
        
        for sector_code in window_data.index.get_level_values('sector_code').unique():
            sector_data = window_data.loc[
                window_data.index.get_level_values('sector_code') == sector_code
            ]
            
            if len(sector_data) < 2:
                continue
            
            # 计算累计收益率
            daily_changes = sector_data['change_pct'].values
            cumulative_return = np.prod(1 + daily_changes / 100) - 1
            
            # 获取板块名称
            sector_name = sector_data['sector_name'].iloc[0] if 'sector_name' in sector_data.columns else sector_code
            
            performance_results.append({
                'sector_code': sector_code,
                'sector_name': sector_name,
                'cumulative_return_pct': cumulative_return * 100,
                'avg_daily_change_pct': daily_changes.mean(),
                'volatility': daily_changes.std(),
                'trading_days': len(daily_changes),
                'year': year
            })
        
        if not performance_results:
            return pd.DataFrame()
        
        # 转换为DataFrame并排序
        performance_df = pd.DataFrame(performance_results)
        performance_df = performance_df.sort_values('cumulative_return_pct', ascending=False)
        performance_df.reset_index(drop=True, inplace=True)
        
        return performance_df
    
    def _calculate_performance_stats(self, performance: pd.DataFrame) -> Dict:
        """计算表现统计信息"""
        if performance.empty:
            return {}
        
        returns = performance['cumulative_return_pct']
        
        return {
            'total_sectors': len(performance),
            'avg_return': returns.mean(),
            'median_return': returns.median(),
            'max_return': returns.max(),
            'min_return': returns.min(),
            'std_return': returns.std(),
            'positive_sectors': len(performance[performance['cumulative_return_pct'] > 0]),
            'negative_sectors': len(performance[performance['cumulative_return_pct'] < 0]),
            'top_sector': performance.iloc[0]['sector_name'],
            'top_return': performance.iloc[0]['cumulative_return_pct'],
            'bottom_sector': performance.iloc[-1]['sector_name'],
            'bottom_return': performance.iloc[-1]['cumulative_return_pct']
        }

    def _generate_yearly_comparison(self, window_analysis: Dict) -> Dict:
        """生成年度对比分析"""
        comparison = {}

        for window_days, yearly_data in window_analysis.items():
            if not yearly_data:
                continue

            years = sorted(yearly_data.keys())
            comparison[window_days] = {
                'years_analyzed': years,
                'yearly_stats': {},
                'best_year': None,
                'worst_year': None,
                'most_consistent_year': None
            }

            # 收集各年统计数据
            year_stats = {}
            avg_returns = {}

            for year in years:
                stats = yearly_data[year]['stats']
                year_stats[year] = stats
                avg_returns[year] = stats['avg_return']

            comparison[window_days]['yearly_stats'] = year_stats

            # 找出最佳和最差年份
            if avg_returns:
                best_year = max(avg_returns, key=avg_returns.get)
                worst_year = min(avg_returns, key=avg_returns.get)

                comparison[window_days]['best_year'] = {
                    'year': best_year,
                    'avg_return': avg_returns[best_year],
                    'top_sector': year_stats[best_year]['top_sector']
                }

                comparison[window_days]['worst_year'] = {
                    'year': worst_year,
                    'avg_return': avg_returns[worst_year],
                    'top_sector': year_stats[worst_year]['top_sector']
                }

                # 找出最一致的年份（标准差最小）
                std_returns = {year: stats['std_return'] for year, stats in year_stats.items()}
                most_consistent_year = min(std_returns, key=std_returns.get)

                comparison[window_days]['most_consistent_year'] = {
                    'year': most_consistent_year,
                    'std_return': std_returns[most_consistent_year],
                    'avg_return': avg_returns[most_consistent_year]
                }

        return comparison

    def _generate_trend_analysis(self, window_analysis: Dict) -> Dict:
        """生成趋势分析"""
        trend_analysis = {}

        for window_days, yearly_data in window_analysis.items():
            if len(yearly_data) < 3:  # 至少需要3年数据才能分析趋势
                continue

            years = sorted(yearly_data.keys())

            # 收集时间序列数据
            avg_returns = []
            max_returns = []
            volatilities = []
            positive_ratios = []

            for year in years:
                stats = yearly_data[year]['stats']
                avg_returns.append(stats['avg_return'])
                max_returns.append(stats['max_return'])
                volatilities.append(stats['std_return'])
                positive_ratios.append(stats['positive_sectors'] / stats['total_sectors'] * 100)

            # 计算趋势
            trend_analysis[window_days] = {
                'years': years,
                'avg_return_trend': self._calculate_trend(years, avg_returns),
                'max_return_trend': self._calculate_trend(years, max_returns),
                'volatility_trend': self._calculate_trend(years, volatilities),
                'positive_ratio_trend': self._calculate_trend(years, positive_ratios),
                'sector_consistency': self._analyze_sector_consistency(yearly_data)
            }

        return trend_analysis

    def _calculate_trend(self, years: List[int], values: List[float]) -> Dict:
        """计算趋势"""
        if len(years) < 2:
            return {'trend': 'insufficient_data'}

        # 简单线性回归计算趋势
        x = np.array(years)
        y = np.array(values)

        # 计算斜率
        n = len(x)
        slope = (n * np.sum(x * y) - np.sum(x) * np.sum(y)) / (n * np.sum(x**2) - (np.sum(x))**2)

        # 计算相关系数
        correlation = np.corrcoef(x, y)[0, 1] if not np.isnan(np.corrcoef(x, y)[0, 1]) else 0

        # 判断趋势方向
        if abs(slope) < 0.1:
            trend_direction = 'stable'
        elif slope > 0:
            trend_direction = 'increasing'
        else:
            trend_direction = 'decreasing'

        return {
            'trend': trend_direction,
            'slope': slope,
            'correlation': correlation,
            'values': list(zip(years, values)),
            'change_per_year': slope,
            'total_change': values[-1] - values[0] if values else 0
        }

    def _analyze_sector_consistency(self, yearly_data: Dict) -> Dict:
        """分析板块一致性"""
        # 找出在所有年份都表现良好的板块
        all_sectors = set()
        yearly_top_sectors = {}

        for year, data in yearly_data.items():
            performance = data['performance']
            if not performance.empty:
                # 获取前10%的板块
                top_n = max(1, len(performance) // 10)
                top_sectors = set(performance.head(top_n)['sector_code'].tolist())
                yearly_top_sectors[year] = top_sectors
                all_sectors.update(top_sectors)

        # 计算每个板块在各年的表现一致性
        sector_consistency = {}

        for sector in all_sectors:
            appearances = 0
            total_years = len(yearly_top_sectors)

            for year, top_sectors in yearly_top_sectors.items():
                if sector in top_sectors:
                    appearances += 1

            consistency_rate = appearances / total_years if total_years > 0 else 0

            if consistency_rate >= 0.5:  # 至少在一半的年份表现良好
                sector_consistency[sector] = {
                    'consistency_rate': consistency_rate,
                    'appearances': appearances,
                    'total_years': total_years
                }

        # 按一致性排序
        consistent_sectors = sorted(
            sector_consistency.items(),
            key=lambda x: x[1]['consistency_rate'],
            reverse=True
        )

        return {
            'consistent_performers': consistent_sectors[:10],  # 前10个最一致的板块
            'total_sectors_analyzed': len(all_sectors),
            'consistency_threshold': 0.5
        }

    def _generate_multi_year_rankings(self, yearly_data: Dict) -> Dict:
        """
        生成多年份排行榜数据

        Args:
            yearly_data: 各年份的表现数据

        Returns:
            多年份排行榜数据字典
        """
        multi_year_rankings = {}

        # 定义目标年份（2020-2025）
        target_years = [2025, 2024, 2023, 2022, 2021, 2020]

        for year in target_years:
            if year in yearly_data and 'performance' in yearly_data[year]:
                performance_df = yearly_data[year]['performance']
                if not performance_df.empty:
                    # 取前20名，确保有足够的数据展示
                    top_performers = performance_df.head(20).copy()

                    # 添加排名列
                    top_performers['rank'] = range(1, len(top_performers) + 1)

                    # 格式化数据
                    formatted_data = []
                    for idx, row in top_performers.iterrows():
                        formatted_data.append({
                            'rank': row['rank'],
                            'sector_code': row['sector_code'],
                            'sector_name': row['sector_name'],
                            'cumulative_return_pct': round(row['cumulative_return_pct'], 2),
                            'avg_daily_change_pct': round(row['avg_daily_change_pct'], 2),
                            'volatility': round(row['volatility'], 2),
                            'trading_days': row['trading_days']
                        })

                    multi_year_rankings[year] = {
                        'data': formatted_data,
                        'end_date': yearly_data[year]['end_date'].strftime('%Y-%m-%d'),
                        'total_sectors': len(performance_df)
                    }
                else:
                    multi_year_rankings[year] = {
                        'data': [],
                        'end_date': '',
                        'total_sectors': 0,
                        'message': '该年份无数据'
                    }
            else:
                multi_year_rankings[year] = {
                    'data': [],
                    'end_date': '',
                    'total_sectors': 0,
                    'message': '该年份无数据'
                }

        return multi_year_rankings
