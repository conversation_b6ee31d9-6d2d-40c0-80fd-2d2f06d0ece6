"""
报告生成模块
负责生成各种格式的分析报告
"""

import pandas as pd
import numpy as np
from pathlib import Path
from typing import Dict, List, Optional, Union
import logging
import os
import sys
import json
from datetime import datetime
import base64
from tabulate import tabulate
import warnings

# 添加项目根目录到路径
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))
import config

# 忽略警告
warnings.filterwarnings('ignore', category=UserWarning)

class ReportGenerator:
    """报告生成器类"""
    
    def __init__(self, output_dir: str = 'output'):
        """
        初始化报告生成器
        
        Args:
            output_dir: 输出目录路径
        """
        self.output_dir = Path(output_dir)
        self.output_dir.mkdir(parents=True, exist_ok=True)
        
        self.logger = self._setup_logger()
        
        # 创建模板目录
        self.template_dir = Path('templates')
        self.template_dir.mkdir(parents=True, exist_ok=True)
        
        self.logger.info(f"报告生成器初始化完成，输出目录: {self.output_dir}")
    
    def _setup_logger(self) -> logging.Logger:
        """设置日志记录器"""
        logger = logging.getLogger(__name__)
        if not logger.handlers:
            handler = logging.StreamHandler()
            formatter = logging.Formatter(
                '%(asctime)s - %(name)s - %(levelname)s - %(message)s'
            )
            handler.setFormatter(formatter)
            logger.addHandler(handler)
            logger.setLevel(logging.INFO)
        return logger
    
    def format_console_table(self, data: pd.DataFrame, title: str, 
                           max_rows: int = 20, tablefmt: str = 'grid') -> str:
        """
        格式化控制台表格输出
        
        Args:
            data: 要显示的数据
            title: 表格标题
            max_rows: 最大显示行数
            tablefmt: 表格格式
            
        Returns:
            格式化后的表格字符串
        """
        if data.empty:
            return f"\n{title}\n{'='*len(title)}\n⚠️ 没有数据\n"
        
        # 限制显示行数
        display_data = data.head(max_rows).copy()
        
        # 格式化数值列
        for col in display_data.columns:
            if display_data[col].dtype in ['float64', 'float32']:
                if 'pct' in col.lower() or '率' in col or '幅' in col:
                    display_data[col] = display_data[col].apply(lambda x: f"{x:.2f}%" if pd.notna(x) else "N/A")
                else:
                    display_data[col] = display_data[col].apply(lambda x: f"{x:.2f}" if pd.notna(x) else "N/A")
            elif display_data[col].dtype in ['int64', 'int32']:
                display_data[col] = display_data[col].apply(lambda x: f"{x:,}" if pd.notna(x) else "N/A")
        
        # 生成表格
        table_str = tabulate(
            display_data.values,
            headers=display_data.columns,
            tablefmt=tablefmt,
            numalign='right',
            stralign='left'
        )
        
        # 添加标题和边框
        title_line = f"\n{title}"
        separator = "=" * len(title)
        
        result = f"{title_line}\n{separator}\n{table_str}\n"
        
        if len(data) > max_rows:
            result += f"\n... 还有 {len(data) - max_rows} 行数据\n"
        
        return result
    
    def export_to_csv(self, data_dict: Dict[str, pd.DataFrame], 
                     filename_prefix: str = "analysis") -> List[str]:
        """
        导出数据到CSV文件
        
        Args:
            data_dict: 数据字典，键为数据名称，值为DataFrame
            filename_prefix: 文件名前缀
            
        Returns:
            导出的文件路径列表
        """
        exported_files = []
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        
        try:
            for data_name, df in data_dict.items():
                if df.empty:
                    continue
                
                # 生成文件名
                filename = f"{filename_prefix}_{data_name}_{timestamp}.csv"
                filepath = self.output_dir / filename
                
                # 导出CSV
                df.to_csv(filepath, index=False, encoding='utf-8-sig')
                exported_files.append(str(filepath))
                
                self.logger.info(f"CSV文件已导出: {filename}")
            
            return exported_files
            
        except Exception as e:
            self.logger.error(f"CSV导出失败: {str(e)}")
            return exported_files
    
    def export_to_json(self, data_dict: Dict, filename: str = "analysis_data.json") -> str:
        """
        导出数据到JSON文件
        
        Args:
            data_dict: 要导出的数据字典
            filename: 文件名
            
        Returns:
            导出的文件路径
        """
        try:
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
            filename_with_timestamp = f"{filename.split('.')[0]}_{timestamp}.json"
            filepath = self.output_dir / filename_with_timestamp
            
            # 处理DataFrame和其他不可序列化的对象
            serializable_data = self._make_serializable(data_dict)
            
            # 导出JSON
            with open(filepath, 'w', encoding='utf-8') as f:
                json.dump(serializable_data, f, ensure_ascii=False, indent=2, default=str)
            
            self.logger.info(f"JSON文件已导出: {filename_with_timestamp}")
            return str(filepath)
            
        except Exception as e:
            self.logger.error(f"JSON导出失败: {str(e)}")
            return ""
    
    def _make_serializable(self, obj):
        """将对象转换为可序列化格式"""
        if isinstance(obj, pd.DataFrame):
            return obj.to_dict('records')
        elif isinstance(obj, pd.Series):
            return obj.to_dict()
        elif isinstance(obj, np.ndarray):
            return obj.tolist()
        elif isinstance(obj, (np.int64, np.int32, np.float64, np.float32)):
            return obj.item()
        elif isinstance(obj, dict):
            return {k: self._make_serializable(v) for k, v in obj.items()}
        elif isinstance(obj, list):
            return [self._make_serializable(item) for item in obj]
        elif isinstance(obj, (datetime, pd.Timestamp)):
            return obj.strftime('%Y-%m-%d %H:%M:%S')
        else:
            return obj

    def generate_html_report(self, analysis_data: Dict, chart_files: List[str] = None,
                           report_title: str = "A股板块数据分析报告") -> str:
        """
        生成HTML综合报告

        Args:
            analysis_data: 分析数据字典
            chart_files: 图表文件路径列表
            report_title: 报告标题

        Returns:
            生成的HTML文件路径
        """
        try:
            # 创建HTML模板
            html_template = self._create_html_template()

            # 生成报告内容
            report_content = self._generate_report_content(analysis_data, chart_files)

            # 替换模板中的占位符
            html_content = html_template.replace('{{REPORT_TITLE}}', report_title)
            html_content = html_content.replace('{{REPORT_CONTENT}}', report_content)
            html_content = html_content.replace('{{GENERATION_TIME}}',
                                              datetime.now().strftime('%Y-%m-%d %H:%M:%S'))

            # 保存HTML文件
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
            filename = f"analysis_report_{timestamp}.html"
            filepath = self.output_dir / filename

            with open(filepath, 'w', encoding='utf-8') as f:
                f.write(html_content)

            self.logger.info(f"HTML报告已生成: {filename}")
            return str(filepath)

        except Exception as e:
            self.logger.error(f"HTML报告生成失败: {str(e)}")
            return ""

    def _create_html_template(self) -> str:
        """创建HTML报告模板"""
        template = """
<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>{{REPORT_TITLE}}</title>
    <style>
        body {
            font-family: 'Microsoft YaHei', 'SimHei', Arial, sans-serif;
            line-height: 1.6;
            margin: 0;
            padding: 20px;
            background-color: #f5f5f5;
        }
        .container {
            max-width: 1400px;
            margin: 0 auto;
            background-color: white;
            padding: 30px;
            border-radius: 10px;
            box-shadow: 0 0 20px rgba(0,0,0,0.1);
        }
        .header {
            text-align: center;
            border-bottom: 3px solid #2c3e50;
            padding-bottom: 20px;
            margin-bottom: 30px;
        }
        .header h1 {
            color: #2c3e50;
            margin: 0;
            font-size: 2.5em;
        }
        .header .subtitle {
            color: #7f8c8d;
            font-size: 1.1em;
            margin-top: 10px;
        }
        .section {
            margin-bottom: 40px;
        }
        .section h2 {
            color: #34495e;
            border-left: 5px solid #3498db;
            padding-left: 15px;
            margin-bottom: 20px;
        }
        .section h3 {
            color: #2c3e50;
            margin-bottom: 15px;
        }

        /* 标签页样式 */
        .tabs-container {
            margin-bottom: 30px;
        }
        .window-tabs {
            display: flex;
            border-bottom: 2px solid #ddd;
            margin-bottom: 20px;
        }
        .window-tab {
            padding: 12px 24px;
            background-color: #f8f9fa;
            border: 1px solid #ddd;
            border-bottom: none;
            cursor: pointer;
            margin-right: 2px;
            border-radius: 8px 8px 0 0;
            font-weight: bold;
            transition: all 0.3s ease;
        }
        .window-tab:hover {
            background-color: #e9ecef;
        }
        .window-tab.active {
            background-color: #3498db;
            color: white;
            border-color: #3498db;
        }
        .window-content {
            display: none;
        }
        .window-content.active {
            display: block;
        }

        .year-tabs {
            display: flex;
            flex-wrap: wrap;
            gap: 5px;
            margin-bottom: 20px;
            padding: 15px;
            background-color: #f8f9fa;
            border-radius: 8px;
        }
        .year-tab {
            padding: 8px 16px;
            background-color: white;
            border: 1px solid #ddd;
            border-radius: 20px;
            cursor: pointer;
            font-size: 14px;
            font-weight: 500;
            transition: all 0.3s ease;
            min-width: 60px;
            text-align: center;
        }
        .year-tab:hover {
            background-color: #e3f2fd;
            border-color: #2196f3;
        }
        .year-tab.active {
            background-color: #2196f3;
            color: white;
            border-color: #2196f3;
        }
        .year-content {
            display: none;
        }
        .year-content.active {
            display: block;
        }

        .table-container {
            overflow-x: auto;
            margin-bottom: 20px;
        }
        table {
            width: 100%;
            border-collapse: collapse;
            margin-bottom: 20px;
        }
        th, td {
            border: 1px solid #ddd;
            padding: 12px;
            text-align: left;
        }
        th {
            background-color: #3498db;
            color: white;
            font-weight: bold;
        }
        tr:nth-child(even) {
            background-color: #f9f9f9;
        }
        tr:hover {
            background-color: #f5f5f5;
        }
        .summary-box {
            background-color: #ecf0f1;
            border-left: 5px solid #e74c3c;
            padding: 20px;
            margin: 20px 0;
            border-radius: 5px;
        }
        .highlight {
            background-color: #fff3cd;
            border: 1px solid #ffeaa7;
            padding: 10px;
            border-radius: 5px;
            margin: 10px 0;
        }
        .footer {
            text-align: center;
            margin-top: 40px;
            padding-top: 20px;
            border-top: 1px solid #ddd;
            color: #7f8c8d;
        }
        .positive { color: #27ae60; font-weight: bold; }
        .negative { color: #e74c3c; font-weight: bold; }
        .neutral { color: #7f8c8d; }
        .no-data {
            text-align: center;
            color: #7f8c8d;
            font-style: italic;
            padding: 40px;
            background-color: #f8f9fa;
            border-radius: 8px;
        }
    </style>
    <script>
        function showWindowTab(windowDays) {
            // 隐藏所有窗口内容
            const windowContents = document.querySelectorAll('.window-content');
            windowContents.forEach(content => content.classList.remove('active'));

            // 移除所有窗口标签的active类
            const windowTabs = document.querySelectorAll('.window-tab');
            windowTabs.forEach(tab => tab.classList.remove('active'));

            // 显示选中的窗口内容
            document.getElementById('window-' + windowDays).classList.add('active');

            // 激活选中的窗口标签
            event.target.classList.add('active');
        }

        function showYearTab(windowDays, year) {
            // 隐藏该窗口下所有年份内容
            const yearContents = document.querySelectorAll('#window-' + windowDays + ' .year-content');
            yearContents.forEach(content => content.classList.remove('active'));

            // 移除该窗口下所有年份标签的active类
            const yearTabs = document.querySelectorAll('#window-' + windowDays + ' .year-tab');
            yearTabs.forEach(tab => tab.classList.remove('active'));

            // 显示选中的年份内容
            document.getElementById('year-' + windowDays + '-' + year).classList.add('active');

            // 激活选中的年份标签
            event.target.classList.add('active');
        }

        // 页面加载完成后初始化
        document.addEventListener('DOMContentLoaded', function() {
            // 激活第一个窗口标签
            const firstWindowTab = document.querySelector('.window-tab');
            if (firstWindowTab) {
                firstWindowTab.classList.add('active');
                const windowDays = firstWindowTab.getAttribute('onclick').match(/\\d+/)[0];
                document.getElementById('window-' + windowDays).classList.add('active');

                // 激活该窗口下的第一个年份标签
                const firstYearTab = document.querySelector('#window-' + windowDays + ' .year-tab');
                if (firstYearTab) {
                    firstYearTab.classList.add('active');
                    const year = firstYearTab.getAttribute('onclick').match(/\\d{4}/)[0];
                    document.getElementById('year-' + windowDays + '-' + year).classList.add('active');
                }
            }
        });
    </script>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>{{REPORT_TITLE}}</h1>
            <div class="subtitle">生成时间: {{GENERATION_TIME}}</div>
        </div>

        {{REPORT_CONTENT}}

        <div class="footer">
            <p>本报告由A股板块数据分析程序自动生成</p>
            <p>数据来源：真实A股交易数据 | 分析时间：{{GENERATION_TIME}}</p>
        </div>
    </div>
</body>
</html>
        """
        return template.strip()

    def _generate_report_content(self, analysis_data: Dict, chart_files: List[str] = None) -> str:
        """生成报告内容"""
        content_parts = []

        # 1. 执行摘要
        content_parts.append(self._generate_executive_summary(analysis_data))

        # 2. 时间窗口分析结果
        if 'window_performance' in analysis_data:
            content_parts.append(self._generate_window_analysis_section(analysis_data))

        # 3. 排名统计结果
        if 'champions' in analysis_data or 'ranking_frequency' in analysis_data:
            content_parts.append(self._generate_ranking_section(analysis_data))

        # 4. 图表展示
        if chart_files:
            content_parts.append(self._generate_charts_section(chart_files))

        # 5. 历史分析结果
        if 'historical_analysis' in analysis_data:
            content_parts.append(self._generate_historical_analysis_section(analysis_data['historical_analysis']))

        # 6. 详细数据表格
        content_parts.append(self._generate_detailed_tables_section(analysis_data))

        return '\n'.join(content_parts)

    def _generate_executive_summary(self, analysis_data: Dict) -> str:
        """生成执行摘要"""
        summary_parts = ['<div class="section">']
        summary_parts.append('<h2>📊 执行摘要</h2>')

        summary_box = ['<div class="summary-box">']

        # 分析概览
        if 'window_performance' in analysis_data:
            window_count = len(analysis_data['window_performance'])
            summary_box.append(f'<p><strong>时间窗口分析：</strong>共分析 {window_count} 个时间窗口</p>')

            # 找出最佳表现
            best_performance = None
            best_return = float('-inf')
            for window_days, data in analysis_data['window_performance'].items():
                if not data.empty and data.iloc[0]['cumulative_return_pct'] > best_return:
                    best_return = data.iloc[0]['cumulative_return_pct']
                    best_performance = (window_days, data.iloc[0])

            if best_performance:
                window_days, performer = best_performance
                return_class = 'positive' if best_return > 0 else 'negative' if best_return < 0 else 'neutral'
                summary_box.append(f'<p><strong>最佳整体表现：</strong>{performer["sector_name"]} '
                                 f'({window_days}日窗口, <span class="{return_class}">{best_return:.2f}%</span>)</p>')

        # 冠军统计
        if 'champions' in analysis_data and not analysis_data['champions'].empty:
            champions = analysis_data['champions']
            most_champion = champions.iloc[0]
            summary_box.append(f'<p><strong>最多冠军板块：</strong>{most_champion["sector_name"]} '
                             f'({most_champion["champion_count"]}次)</p>')

        # 前10频次
        if 'ranking_frequency' in analysis_data and not analysis_data['ranking_frequency'].empty:
            frequency = analysis_data['ranking_frequency']
            most_frequent = frequency.iloc[0]
            summary_box.append(f'<p><strong>最频繁前10板块：</strong>{most_frequent["sector_name"]} '
                             f'({most_frequent["top10_count"]}次)</p>')

        summary_box.append('</div>')
        summary_parts.extend(summary_box)
        summary_parts.append('</div>')

        return '\n'.join(summary_parts)

    def _generate_window_analysis_section(self, analysis_data: Dict) -> str:
        """生成时间窗口分析部分（支持多年份标签页）"""
        section_parts = ['<div class="section">']
        section_parts.append('<h2>📈 时间窗口累计涨跌幅分析（多年份对比）</h2>')

        # 检查是否有历史分析数据
        historical_analysis = analysis_data.get('historical_analysis', {})
        multi_year_rankings = historical_analysis.get('multi_year_rankings', {})

        if not multi_year_rankings:
            # 如果没有历史数据，使用原有的单年份显示方式
            window_data = analysis_data.get('window_performance', {})
            section_parts.append('<p>⚠️ 未找到多年份历史数据，显示当前年份数据</p>')

            for window_days in sorted(window_data.keys()):
                data = window_data[window_days]
                if data.empty:
                    continue

                section_parts.append(f'<h3>{window_days}日时间窗口表现排行榜</h3>')

                # 生成表格
                table_html = self._dataframe_to_html_table(
                    data.head(10)[['sector_name', 'sector_code', 'cumulative_return_pct',
                                  'avg_daily_change_pct', 'volatility']],
                    columns_rename={
                        'sector_name': '板块名称',
                        'sector_code': '板块代码',
                        'cumulative_return_pct': '累计收益率(%)',
                        'avg_daily_change_pct': '平均日收益率(%)',
                        'volatility': '波动率(%)'
                    }
                )

                section_parts.append('<div class="table-container">')
                section_parts.append(table_html)
                section_parts.append('</div>')
        else:
            # 生成多年份标签页
            section_parts.append(self._generate_multi_year_tabs(multi_year_rankings))

        section_parts.append('</div>')
        return '\n'.join(section_parts)

    def _generate_multi_year_tabs(self, multi_year_rankings: Dict) -> str:
        """生成多年份标签页HTML"""
        tabs_parts = ['<div class="tabs-container">']

        # 获取所有时间窗口
        window_days_list = sorted(multi_year_rankings.keys())

        # 生成时间窗口标签
        tabs_parts.append('<div class="window-tabs">')
        for i, window_days in enumerate(window_days_list):
            active_class = 'active' if i == 0 else ''
            tabs_parts.append(f'<div class="window-tab {active_class}" onclick="showWindowTab({window_days})">')
            tabs_parts.append(f'{window_days}日时间窗口')
            tabs_parts.append('</div>')
        tabs_parts.append('</div>')

        # 生成每个时间窗口的内容
        for i, window_days in enumerate(window_days_list):
            active_class = 'active' if i == 0 else ''
            tabs_parts.append(f'<div id="window-{window_days}" class="window-content {active_class}">')

            # 获取该时间窗口的年份数据
            year_data = multi_year_rankings[window_days]
            available_years = sorted([year for year in year_data.keys() if year_data[year]['data']], reverse=True)

            if not available_years:
                tabs_parts.append('<div class="no-data">该时间窗口暂无历史数据</div>')
            else:
                # 生成年份标签
                tabs_parts.append('<div class="year-tabs">')
                for j, year in enumerate(available_years):
                    active_class = 'active' if j == 0 else ''
                    tabs_parts.append(f'<div class="year-tab {active_class}" onclick="showYearTab({window_days}, {year})">')
                    tabs_parts.append(f'{year}年')
                    tabs_parts.append('</div>')
                tabs_parts.append('</div>')

                # 生成每年的排行榜内容
                for j, year in enumerate(available_years):
                    active_class = 'active' if j == 0 else ''
                    tabs_parts.append(f'<div id="year-{window_days}-{year}" class="year-content {active_class}">')

                    year_info = year_data[year]
                    if year_info['data']:
                        # 生成表格
                        tabs_parts.append(f'<h4>{year}年 {window_days}日时间窗口排行榜</h4>')
                        tabs_parts.append(f'<p><strong>数据截止日期：</strong>{year_info["end_date"]} | ')
                        tabs_parts.append(f'<strong>总板块数：</strong>{year_info["total_sectors"]}个</p>')

                        # 创建DataFrame用于生成表格
                        import pandas as pd
                        df = pd.DataFrame(year_info['data'])

                        table_html = self._dataframe_to_html_table(
                            df[['rank', 'sector_name', 'sector_code', 'cumulative_return_pct',
                               'avg_daily_change_pct', 'volatility', 'trading_days']],
                            columns_rename={
                                'rank': '排名',
                                'sector_name': '板块名称',
                                'sector_code': '板块代码',
                                'cumulative_return_pct': '累计收益率(%)',
                                'avg_daily_change_pct': '平均日收益率(%)',
                                'volatility': '波动率(%)',
                                'trading_days': '交易天数'
                            }
                        )

                        tabs_parts.append('<div class="table-container">')
                        tabs_parts.append(table_html)
                        tabs_parts.append('</div>')
                    else:
                        tabs_parts.append('<div class="no-data">')
                        tabs_parts.append(f'{year_info.get("message", "该年份无数据")}')
                        tabs_parts.append('</div>')

                    tabs_parts.append('</div>')  # 关闭year-content

            tabs_parts.append('</div>')  # 关闭window-content

        tabs_parts.append('</div>')  # 关闭tabs-container
        return '\n'.join(tabs_parts)

    def _generate_ranking_section(self, analysis_data: Dict) -> str:
        """生成排名统计部分"""
        section_parts = ['<div class="section">']
        section_parts.append('<h2>🏆 排名统计分析</h2>')

        # 单日冠军统计
        if 'champions' in analysis_data and not analysis_data['champions'].empty:
            champions = analysis_data['champions']
            section_parts.append('<h3>单日冠军次数排行榜</h3>')

            table_html = self._dataframe_to_html_table(
                champions.head(10)[['sector_name', 'sector_code', 'champion_count',
                                   'champion_frequency_pct', 'avg_champion_change_pct']],
                columns_rename={
                    'sector_name': '板块名称',
                    'sector_code': '板块代码',
                    'champion_count': '冠军次数',
                    'champion_frequency_pct': '冠军频率(%)',
                    'avg_champion_change_pct': '平均冠军涨跌幅(%)'
                }
            )

            section_parts.append('<div class="table-container">')
            section_parts.append(table_html)
            section_parts.append('</div>')

        # 前10频次统计
        if 'ranking_frequency' in analysis_data and not analysis_data['ranking_frequency'].empty:
            frequency = analysis_data['ranking_frequency']
            section_parts.append('<h3>前10名频次排行榜</h3>')

            table_html = self._dataframe_to_html_table(
                frequency.head(10)[['sector_name', 'sector_code', 'top10_count',
                                   'top10_frequency_pct', 'top5_count', 'top3_count']],
                columns_rename={
                    'sector_name': '板块名称',
                    'sector_code': '板块代码',
                    'top10_count': '前10次数',
                    'top10_frequency_pct': '前10频率(%)',
                    'top5_count': '前5次数',
                    'top3_count': '前3次数'
                }
            )

            section_parts.append('<div class="table-container">')
            section_parts.append(table_html)
            section_parts.append('</div>')

        section_parts.append('</div>')
        return '\n'.join(section_parts)

    def _generate_charts_section(self, chart_files: List[str]) -> str:
        """生成图表展示部分"""
        section_parts = ['<div class="section">']
        section_parts.append('<h2>📊 可视化图表</h2>')

        for chart_file in chart_files:
            if os.path.exists(chart_file):
                # 获取图表文件名
                chart_name = Path(chart_file).name

                # 读取图片并转换为base64
                try:
                    with open(chart_file, 'rb') as f:
                        img_data = f.read()
                    img_base64 = base64.b64encode(img_data).decode('utf-8')

                    section_parts.append('<div class="chart-container">')
                    section_parts.append(f'<h3>{chart_name}</h3>')
                    section_parts.append(f'<img src="data:image/png;base64,{img_base64}" alt="{chart_name}">')
                    section_parts.append('</div>')

                except Exception as e:
                    section_parts.append(f'<p>图表加载失败: {chart_name} - {str(e)}</p>')

        section_parts.append('</div>')
        return '\n'.join(section_parts)

    def _generate_historical_analysis_section(self, historical_data: Dict) -> str:
        """生成历史分析部分"""
        section_parts = ['<div class="section">']
        section_parts.append('<h2>📊 历史同期分析</h2>')

        if not historical_data:
            section_parts.append('<p>没有历史分析数据</p>')
            section_parts.append('</div>')
            return '\n'.join(section_parts)

        # 基本信息
        target_date = historical_data.get('target_date', '')
        target_month_day = historical_data.get('target_month_day', '')
        historical_dates = historical_data.get('historical_dates', {})

        section_parts.append(f'<div class="summary-box">')
        section_parts.append(f'<h3>分析概览</h3>')
        section_parts.append(f'<p><strong>目标日期：</strong>{target_date}</p>')
        section_parts.append(f'<p><strong>分析周期：</strong>每年{target_month_day}同期</p>')
        section_parts.append(f'<p><strong>涵盖年份：</strong>{len(historical_dates)}年 ({min(historical_dates.keys())}-{max(historical_dates.keys())})</p>')
        section_parts.append('</div>')

        # 年度对比分析
        yearly_comparison = historical_data.get('yearly_comparison', {})
        if yearly_comparison:
            section_parts.append('<h3>年度对比分析</h3>')

            for window_days, comparison in yearly_comparison.items():
                section_parts.append(f'<h4>{window_days}日时间窗口年度对比</h4>')

                # 最佳和最差年份
                best_year = comparison.get('best_year', {})
                worst_year = comparison.get('worst_year', {})

                if best_year and worst_year:
                    section_parts.append('<div class="highlight">')
                    section_parts.append(f'<p><strong>最佳年份：</strong>{best_year["year"]}年 '
                                        f'(平均收益率: <span class="positive">{best_year["avg_return"]:.2f}%</span>, '
                                        f'最佳板块: {best_year["top_sector"]})</p>')
                    section_parts.append(f'<p><strong>最差年份：</strong>{worst_year["year"]}年 '
                                        f'(平均收益率: <span class="negative">{worst_year["avg_return"]:.2f}%</span>, '
                                        f'最佳板块: {worst_year["top_sector"]})</p>')
                    section_parts.append('</div>')

                # 年度统计表格
                yearly_stats = comparison.get('yearly_stats', {})
                if yearly_stats:
                    table_data = []
                    for year, stats in sorted(yearly_stats.items()):
                        table_data.append({
                            '年份': year,
                            '平均收益率(%)': f"{stats['avg_return']:.2f}",
                            '最大收益率(%)': f"{stats['max_return']:.2f}",
                            '波动率(%)': f"{stats['std_return']:.2f}",
                            '上涨板块数': stats['positive_sectors'],
                            '下跌板块数': stats['negative_sectors'],
                            '最佳板块': stats['top_sector']
                        })

                    if table_data:
                        table_df = pd.DataFrame(table_data)
                        table_html = self._dataframe_to_html_table(table_df)
                        section_parts.append('<div class="table-container">')
                        section_parts.append(table_html)
                        section_parts.append('</div>')

        # 趋势分析
        trend_analysis = historical_data.get('trend_analysis', {})
        if trend_analysis:
            section_parts.append('<h3>趋势分析</h3>')

            for window_days, trend_data in trend_analysis.items():
                section_parts.append(f'<h4>{window_days}日时间窗口趋势</h4>')

                # 平均收益率趋势
                avg_return_trend = trend_data.get('avg_return_trend', {})
                if avg_return_trend.get('trend') != 'insufficient_data':
                    trend_direction = avg_return_trend['trend']
                    trend_class = 'positive' if trend_direction == 'increasing' else 'negative' if trend_direction == 'decreasing' else 'neutral'

                    section_parts.append('<div class="highlight">')
                    section_parts.append(f'<p><strong>平均收益率趋势：</strong>'
                                        f'<span class="{trend_class}">{self._translate_trend(trend_direction)}</span></p>')
                    section_parts.append(f'<p><strong>年均变化：</strong>{avg_return_trend["change_per_year"]:.2f}%</p>')
                    section_parts.append(f'<p><strong>总体变化：</strong>{avg_return_trend["total_change"]:.2f}%</p>')
                    section_parts.append('</div>')

                # 一致性表现板块
                sector_consistency = trend_data.get('sector_consistency', {})
                consistent_performers = sector_consistency.get('consistent_performers', [])

                if consistent_performers:
                    section_parts.append('<h5>一致性表现板块</h5>')
                    section_parts.append('<p>以下板块在多数年份都表现优异：</p>')
                    section_parts.append('<ul>')

                    for sector_code, consistency_data in consistent_performers[:5]:
                        rate = consistency_data['consistency_rate'] * 100
                        appearances = consistency_data['appearances']
                        total_years = consistency_data['total_years']
                        section_parts.append(f'<li>{sector_code}: {rate:.1f}% 一致性 '
                                            f'({appearances}/{total_years}年表现优异)</li>')

                    section_parts.append('</ul>')

        section_parts.append('</div>')
        return '\n'.join(section_parts)

    def _translate_trend(self, trend: str) -> str:
        """翻译趋势方向"""
        translations = {
            'increasing': '上升趋势',
            'decreasing': '下降趋势',
            'stable': '稳定趋势'
        }
        return translations.get(trend, trend)

    def _generate_detailed_tables_section(self, analysis_data: Dict) -> str:
        """生成详细数据表格部分"""
        section_parts = ['<div class="section">']
        section_parts.append('<h2>📋 详细数据表格</h2>')

        # 这里可以添加更详细的数据表格
        section_parts.append('<p>详细数据已导出到CSV文件中，请查看输出目录。</p>')

        section_parts.append('</div>')
        return '\n'.join(section_parts)

    def _dataframe_to_html_table(self, df: pd.DataFrame, columns_rename: Dict = None) -> str:
        """将DataFrame转换为HTML表格"""
        if df.empty:
            return '<p>没有数据</p>'

        # 重命名列
        display_df = df.copy()
        if columns_rename:
            display_df = display_df.rename(columns=columns_rename)

        # 格式化数值
        for col in display_df.columns:
            if display_df[col].dtype in ['float64', 'float32']:
                if '%' in col or '率' in col or '幅' in col:
                    display_df[col] = display_df[col].apply(
                        lambda x: f'<span class="{"positive" if x > 0 else "negative" if x < 0 else "neutral"}">{x:.2f}%</span>'
                        if pd.notna(x) else 'N/A'
                    )
                else:
                    display_df[col] = display_df[col].apply(lambda x: f'{x:.2f}' if pd.notna(x) else 'N/A')
            elif display_df[col].dtype in ['int64', 'int32']:
                display_df[col] = display_df[col].apply(lambda x: f'{x:,}' if pd.notna(x) else 'N/A')

        # 生成HTML表格
        return display_df.to_html(escape=False, index=False, classes='table table-striped')

    def generate_summary_report(self, analysis_data: Dict) -> str:
        """生成摘要报告文本"""
        report_lines = []
        report_lines.append("=" * 80)
        report_lines.append("A股板块数据分析摘要报告")
        report_lines.append("=" * 80)
        report_lines.append(f"生成时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
        report_lines.append("")

        # 时间窗口分析摘要
        if 'window_performance' in analysis_data:
            report_lines.append("📈 时间窗口分析摘要:")
            report_lines.append("-" * 40)

            for window_days, data in analysis_data['window_performance'].items():
                if not data.empty:
                    best_performer = data.iloc[0]
                    report_lines.append(f"  {window_days:2d}日窗口最佳: {best_performer['sector_name']} "
                                      f"({best_performer['cumulative_return_pct']:.2f}%)")
            report_lines.append("")

        # 排名统计摘要
        if 'champions' in analysis_data and not analysis_data['champions'].empty:
            champions = analysis_data['champions']
            most_champion = champions.iloc[0]
            report_lines.append("🏆 排名统计摘要:")
            report_lines.append("-" * 40)
            report_lines.append(f"  最多冠军板块: {most_champion['sector_name']} "
                              f"({most_champion['champion_count']}次)")

            if 'ranking_frequency' in analysis_data and not analysis_data['ranking_frequency'].empty:
                frequency = analysis_data['ranking_frequency']
                most_frequent = frequency.iloc[0]
                report_lines.append(f"  最频繁前10板块: {most_frequent['sector_name']} "
                                  f"({most_frequent['top10_count']}次)")
            report_lines.append("")

        report_lines.append("=" * 80)

        return '\n'.join(report_lines)
